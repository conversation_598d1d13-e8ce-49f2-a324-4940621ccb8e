package repository

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"gitlab.zalopay.vn/phunn4/coupon-product-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/redis"
)

const (
	productCachePrefixByID  = "product:id:"
	productCacheTTL         = 10 * time.Minute
	categoryCachePrefixByID = "category:id:"
	categoryCacheTTL        = 30 * time.Minute
	categoriesListCacheKey  = "categories:list"
	categoriesListCacheTTL  = 30 * time.Minute
)

type productInfrastructure struct {
	db     *database.DB
	redis  *redis.Client
	logger *logging.Logger
}

type ProductRepository interface {
	// Product operations
	GetProductByID(ctx context.Context, id uint64) (*model.Product, error)
	UpdateProduct(ctx context.Context, product *model.Product) error
	ListProducts(ctx context.Context, req *model.ListProductsRequest) ([]*model.Product, int64, error)

	// Category operations
	ListCategories(ctx context.Context) ([]*model.Category, error)
}

func NewProductRepository(db *database.DB, redis *redis.Client, logger *logging.Logger) ProductRepository {
	return &productInfrastructure{
		db:     db,
		redis:  redis,
		logger: logger,
	}
}

// Redis caching helper methods
func (r *productInfrastructure) getProductFromCache(ctx context.Context, key string) (*model.Product, error) {
	log := r.logger.WithContext(ctx)

	val, err := r.redis.Get(ctx, key)
	if err != nil || val == "" {
		return nil, err
	}

	log.Debugf("Cache hit for key: %s", key)
	var product model.Product
	if err := json.Unmarshal([]byte(val), &product); err != nil {
		log.Errorf("Failed to unmarshal product from cache for key %s: %v", key, err)
		return nil, err
	}
	return &product, nil
}

func (r *productInfrastructure) setProductInCache(ctx context.Context, product *model.Product) {
	log := r.logger.WithContext(ctx)

	productBytes, err := json.Marshal(product)
	if err != nil {
		log.Errorf("Failed to marshal product for caching (ID: %d): %v", product.ID, err)
		return
	}

	keyByID := fmt.Sprintf("%s%d", productCachePrefixByID, product.ID)
	if err := r.redis.Set(ctx, keyByID, productBytes, productCacheTTL); err != nil {
		log.Errorf("Failed to set product cache for key %s: %v", keyByID, err)
	}
}

func (r *productInfrastructure) invalidateProductCache(ctx context.Context, product *model.Product) {
	keyByID := fmt.Sprintf("%s%d", productCachePrefixByID, product.ID)

	if err := r.redis.Del(ctx, keyByID); err != nil {
		r.logger.WithContext(ctx).Errorf("Failed to invalidate product cache for product %d: %v", product.ID, err)
	}
}

// Category caching helper methods
func (r *productInfrastructure) getCategoryFromCache(ctx context.Context, key string) (*model.Category, error) {
	log := r.logger.WithContext(ctx)

	val, err := r.redis.Get(ctx, key)
	if err != nil || val == "" {
		return nil, err
	}

	log.Debugf("Cache hit for key: %s", key)
	var category model.Category
	if err := json.Unmarshal([]byte(val), &category); err != nil {
		log.Errorf("Failed to unmarshal category from cache for key %s: %v", key, err)
		return nil, err
	}
	return &category, nil
}

func (r *productInfrastructure) setCategoryInCache(ctx context.Context, category *model.Category) {
	log := r.logger.WithContext(ctx)

	categoryBytes, err := json.Marshal(category)
	if err != nil {
		log.Errorf("Failed to marshal category for caching (ID: %d): %v", category.ID, err)
		return
	}

	keyByID := fmt.Sprintf("%s%d", categoryCachePrefixByID, category.ID)
	if err := r.redis.Set(ctx, keyByID, categoryBytes, categoryCacheTTL); err != nil {
		log.Errorf("Failed to set category cache for key %s: %v", keyByID, err)
	}
}

// Product operations

func (r *productInfrastructure) GetProductByID(ctx context.Context, id uint64) (*model.Product, error) {
	cacheKey := fmt.Sprintf("%s%d", productCachePrefixByID, id)

	// Try to get from cache first
	cachedProduct, _ := r.getProductFromCache(ctx, cacheKey)
	if cachedProduct != nil {
		return cachedProduct, nil
	}

	var product model.Product
	err := r.db.WithContext(ctx).Preload("Category").First(&product, id).Error
	if err != nil {
		return nil, err
	}

	// Cache the product
	r.setProductInCache(ctx, &product)

	return &product, nil
}

func (r *productInfrastructure) UpdateProduct(ctx context.Context, product *model.Product) error {
	err := r.db.WithContext(ctx).Save(product).Error
	if err == nil {
		// Invalidate cache after successful update
		r.invalidateProductCache(ctx, product)
	}
	return err
}

func (r *productInfrastructure) ListProducts(ctx context.Context, req *model.ListProductsRequest) ([]*model.Product, int64, error) {
	var products []*model.Product
	var total int64

	query := r.db.WithContext(ctx).Model(&model.Product{})

	// Apply filters
	if req.Search != "" {
		searchTerm := "%" + strings.ToLower(req.Search) + "%"
		query = query.Where("LOWER(name) LIKE ? OR LOWER(description) LIKE ?", searchTerm, searchTerm)
	}

	if req.CategoryID != nil {
		query = query.Where("category_id = ?", *req.CategoryID)
	}

	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count products: %w", err)
	}

	// Apply sorting
	sortBy := req.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}
	sortOrder := req.SortOrder
	if sortOrder == "" {
		sortOrder = "desc"
	}
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// Apply pagination
	if req.Page > 0 && req.Limit > 0 {
		offset := (req.Page - 1) * req.Limit
		query = query.Offset(offset).Limit(req.Limit)
	}

	// Execute query with preloading
	if err := query.Preload("Category").Find(&products).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list products: %w", err)
	}

	return products, total, nil
}

// Category operations

func (r *productInfrastructure) ListCategories(ctx context.Context) ([]*model.Category, error) {
	log := r.logger.WithContext(ctx)

	// Try to get from cache first
	val, err := r.redis.Get(ctx, categoriesListCacheKey)
	if err == nil && val != "" {
		log.Debugf("Cache hit for categories list")
		var categories []*model.Category
		if err := json.Unmarshal([]byte(val), &categories); err == nil {
			return categories, nil
		}
		log.Errorf("Failed to unmarshal categories from cache: %v", err)
	}

	var categories []*model.Category

	// Simple query to get all categories, ordered by name
	if err := r.db.WithContext(ctx).Model(&model.Category{}).Order("name ASC").Find(&categories).Error; err != nil {
		return nil, fmt.Errorf("failed to list categories: %w", err)
	}

	// Cache the result
	if resultBytes, err := json.Marshal(categories); err == nil {
		if err := r.redis.Set(ctx, categoriesListCacheKey, resultBytes, categoriesListCacheTTL); err != nil {
			log.Errorf("Failed to cache categories list: %v", err)
		}
	}

	return categories, nil
}
